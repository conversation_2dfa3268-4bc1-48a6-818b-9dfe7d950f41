import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get teacher record for the logged-in user
    const teacher = await prisma.teacher.findUnique({
      where: {
        userId: session.user.id
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })
    }

    // Get teacher's assigned subjects and classes
    const teacherSubjects = await prisma.subject.findMany({
      where: {
        teacherId: teacher.id
      },
      include: {
        class: {
          include: {
            sections: true,
            _count: {
              select: {
                students: true
              }
            }
          }
        },
        exams: {
          where: {
            date: {
              gte: new Date() // Upcoming exams
            }
          },
          orderBy: {
            date: 'asc'
          },
          take: 5
        }
      }
    })

    // Calculate statistics
    const totalClasses = new Set(teacherSubjects.map(s => s.class.id)).size
    const totalStudents = teacherSubjects.reduce((sum, subject) => sum + subject.class._count.students, 0)
    const upcomingExams = teacherSubjects.reduce((sum, subject) => sum + subject.exams.length, 0)

    // Get attendance statistics for teacher's classes (last 30 days)
    const classIds = [...new Set(teacherSubjects.map(s => s.class.id))]
    
    const [attendanceStats, marksStats] = await Promise.all([
      // Attendance statistics
      classIds.length > 0 ? prisma.attendance.aggregate({
        where: {
          classId: {
            in: classIds
          },
          date: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        _count: {
          id: true
        }
      }).then(async (total) => {
        const present = await prisma.attendance.count({
          where: {
            classId: {
              in: classIds
            },
            date: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            status: 'PRESENT'
          }
        })
        return {
          total: total._count.id,
          present,
          rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0
        }
      }) : { total: 0, present: 0, rate: 0 },
      
      // Marks statistics for teacher's graded exams
      prisma.mark.aggregate({
        where: {
          gradedByTeacherId: teacher.id
        },
        _avg: {
          obtainedMarks: true
        },
        _count: {
          id: true
        }
      })
    ])

    // Get assigned classes with details
    const assignedClasses = await Promise.all(
      teacherSubjects.map(async (subject) => {
        // Get recent attendance for this class
        const recentAttendance = await prisma.attendance.aggregate({
          where: {
            classId: subject.class.id,
            date: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
            }
          },
          _count: { id: true }
        }).then(async (total) => {
          const present = await prisma.attendance.count({
            where: {
              classId: subject.class.id,
              date: {
                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
              },
              status: 'PRESENT'
            }
          })
          return total._count.id > 0 ? (present / total._count.id) * 100 : 0
        })

        return {
          id: subject.class.id,
          name: subject.class.name,
          subject: subject.name,
          students: subject.class._count.students,
          attendance: Math.round(recentAttendance * 10) / 10,
          nextExam: subject.exams[0] ? {
            name: subject.exams[0].name,
            date: subject.exams[0].date
          } : null
        }
      })
    )

    const stats = {
      totalClasses,
      totalStudents,
      averageAttendance: Math.round(attendanceStats.rate * 10) / 10,
      averageMarks: marksStats._avg.obtainedMarks 
        ? Math.round(marksStats._avg.obtainedMarks * 10) / 10 
        : 0,
      upcomingExams,
      totalMarksGraded: marksStats._count.id,
      assignedClasses
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching teacher dashboard stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
