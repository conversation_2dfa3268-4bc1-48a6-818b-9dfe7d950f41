import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get student record for the logged-in user
    const student = await prisma.student.findUnique({
      where: {
        userId: session.user.id
      },
      include: {
        currentClass: {
          include: {
            subjects: true
          }
        },
        currentSection: true
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Get statistics in parallel
    const [attendanceStats, marksStats, upcomingExams] = await Promise.all([
      // Attendance statistics (last 30 days)
      prisma.attendance.aggregate({
        where: {
          studentId: student.id,
          date: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        _count: {
          id: true
        }
      }).then(async (total) => {
        const present = await prisma.attendance.count({
          where: {
            studentId: student.id,
            date: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            status: 'PRESENT'
          }
        })
        return {
          total: total._count.id,
          present,
          rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0
        }
      }),
      
      // Marks statistics
      prisma.mark.aggregate({
        where: {
          studentId: student.id
        },
        _avg: {
          obtainedMarks: true
        },
        _count: {
          id: true
        }
      }),
      
      // Upcoming exams for student's class
      prisma.exam.findMany({
        where: {
          subject: {
            classId: student.currentClassId
          },
          date: {
            gte: new Date() // Future exams
          }
        },
        include: {
          subject: true
        },
        orderBy: {
          date: 'asc'
        },
        take: 5
      })
    ])

    // Get recent marks with subject details
    const recentMarks = await prisma.mark.findMany({
      where: {
        studentId: student.id
      },
      include: {
        exam: {
          include: {
            subject: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    const stats = {
      currentClass: student.currentClass?.name || 'Not assigned',
      currentSection: student.currentSection?.name || 'Not assigned',
      rollNumber: student.rollNumber || 'Not assigned',
      attendanceRate: Math.round(attendanceStats.rate * 10) / 10,
      averageMarks: marksStats._avg.obtainedMarks 
        ? Math.round(marksStats._avg.obtainedMarks * 10) / 10 
        : 0,
      totalSubjects: student.currentClass?.subjects.length || 0,
      upcomingExams: upcomingExams.length,
      totalMarksRecords: marksStats._count.id,
      totalAttendanceRecords: attendanceStats.total,
      recentMarks: recentMarks.map(mark => ({
        id: mark.id,
        subject: mark.exam.subject.name,
        examName: mark.exam.name,
        obtainedMarks: mark.obtainedMarks,
        maxMarks: mark.exam.maxMarks,
        percentage: Math.round((mark.obtainedMarks / mark.exam.maxMarks) * 100),
        date: mark.exam.date
      })),
      upcomingExamsList: upcomingExams.map(exam => ({
        id: exam.id,
        name: exam.name,
        subject: exam.subject.name,
        date: exam.date,
        maxMarks: exam.maxMarks
      }))
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching student dashboard stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
