{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/marks-validation.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Validation schemas\nexport const MarkEntrySchema = z.object({\n  studentId: z.string().min(1, 'Student ID is required'),\n  examId: z.string().min(1, 'Exam ID is required'),\n  obtainedMarks: z.number()\n    .min(0, 'Marks cannot be negative')\n    .max(1000, 'Marks cannot exceed 1000'), // Will be validated against exam maxMarks\n  remarks: z.string().optional()\n})\n\nexport const BulkMarkEntrySchema = z.object({\n  examId: z.string().min(1, 'Exam ID is required'),\n  marks: z.array(z.object({\n    studentId: z.string().min(1, 'Student ID is required'),\n    obtainedMarks: z.number()\n      .min(0, 'Marks cannot be negative')\n      .max(1000, 'Marks cannot exceed 1000'),\n    remarks: z.string().optional()\n  })).min(1, 'At least one mark entry is required')\n})\n\n// Validation functions\nexport interface ValidationError {\n  field: string\n  message: string\n  studentId?: string\n}\n\nexport interface MarkValidationResult {\n  isValid: boolean\n  errors: ValidationError[]\n}\n\nexport const validateMarkEntry = (\n  studentId: string,\n  examId: string,\n  obtainedMarks: number,\n  maxMarks: number,\n  remarks?: string\n): MarkValidationResult => {\n  const errors: ValidationError[] = []\n\n  // Basic validation\n  if (!studentId || studentId.trim() === '') {\n    errors.push({ field: 'studentId', message: 'Student ID is required' })\n  }\n\n  if (!examId || examId.trim() === '') {\n    errors.push({ field: 'examId', message: 'Exam ID is required' })\n  }\n\n  // Marks validation\n  if (obtainedMarks < 0) {\n    errors.push({ \n      field: 'obtainedMarks', \n      message: 'Marks cannot be negative',\n      studentId \n    })\n  }\n\n  if (obtainedMarks > maxMarks) {\n    errors.push({ \n      field: 'obtainedMarks', \n      message: `Marks cannot exceed maximum marks (${maxMarks})`,\n      studentId \n    })\n  }\n\n  // Check for decimal precision (max 2 decimal places)\n  const decimalPlaces = (obtainedMarks.toString().split('.')[1] || '').length\n  if (decimalPlaces > 2) {\n    errors.push({\n      field: 'obtainedMarks',\n      message: 'Marks can have at most 2 decimal places',\n      studentId\n    })\n  }\n\n  // Remarks validation (optional but if provided, should be reasonable length)\n  if (remarks && remarks.length > 500) {\n    errors.push({ \n      field: 'remarks', \n      message: 'Remarks cannot exceed 500 characters',\n      studentId \n    })\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\nexport const validateBulkMarkEntry = (\n  examId: string,\n  maxMarks: number,\n  marksData: Array<{\n    studentId: string\n    obtainedMarks: number\n    remarks?: string\n  }>\n): MarkValidationResult => {\n  const errors: ValidationError[] = []\n\n  // Exam validation\n  if (!examId || examId.trim() === '') {\n    errors.push({ field: 'examId', message: 'Exam ID is required' })\n  }\n\n  // Check if marks data is provided\n  if (!marksData || marksData.length === 0) {\n    errors.push({ field: 'marks', message: 'At least one mark entry is required' })\n    return { isValid: false, errors }\n  }\n\n  // Validate each mark entry\n  const studentIds = new Set<string>()\n  marksData.forEach((mark, index) => {\n    // Check for duplicate student IDs\n    if (studentIds.has(mark.studentId)) {\n      errors.push({ \n        field: 'studentId', \n        message: 'Duplicate student ID found',\n        studentId: mark.studentId \n      })\n    } else {\n      studentIds.add(mark.studentId)\n    }\n\n    // Validate individual mark entry\n    const validation = validateMarkEntry(\n      mark.studentId,\n      examId,\n      mark.obtainedMarks,\n      maxMarks,\n      mark.remarks\n    )\n\n    // Add any errors from individual validation\n    errors.push(...validation.errors)\n  })\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n// Grade calculation validation\nexport const validateGradeCalculation = (\n  obtainedMarks: number,\n  maxMarks: number\n): { isValid: boolean; percentage?: number; error?: string } => {\n  if (maxMarks <= 0) {\n    return { isValid: false, error: 'Maximum marks must be greater than 0' }\n  }\n\n  if (obtainedMarks < 0) {\n    return { isValid: false, error: 'Obtained marks cannot be negative' }\n  }\n\n  if (obtainedMarks > maxMarks) {\n    return { isValid: false, error: 'Obtained marks cannot exceed maximum marks' }\n  }\n\n  const percentage = Math.round((obtainedMarks / maxMarks) * 100 * 100) / 100\n  return { isValid: true, percentage }\n}\n\n// Error formatting utilities\nexport const formatValidationErrors = (errors: ValidationError[]): string => {\n  if (errors.length === 0) return ''\n  \n  if (errors.length === 1) {\n    return errors[0].message\n  }\n\n  return `Multiple errors found:\\n${errors.map(e => `• ${e.message}`).join('\\n')}`\n}\n\nexport const groupErrorsByStudent = (errors: ValidationError[]): Record<string, ValidationError[]> => {\n  return errors.reduce((acc, error) => {\n    const key = error.studentId || 'general'\n    if (!acc[key]) {\n      acc[key] = []\n    }\n    acc[key].push(error)\n    return acc\n  }, {} as Record<string, ValidationError[]>)\n}\n\n// Common validation patterns\nexport const VALIDATION_PATTERNS = {\n  STUDENT_ID: /^[a-zA-Z0-9-_]+$/,\n  EXAM_ID: /^[a-zA-Z0-9-_]+$/,\n  MARKS_FORMAT: /^\\d+(\\.\\d{1,2})?$/, // Allows up to 2 decimal places\n} as const\n\nexport const validatePattern = (value: string, pattern: RegExp, fieldName: string): ValidationError | null => {\n  if (!pattern.test(value)) {\n    return {\n      field: fieldName,\n      message: `Invalid ${fieldName} format`\n    }\n  }\n  return null\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,kBAAkB,sQAAC,CAAC,MAAM,CAAC;IACtC,WAAW,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,eAAe,sQAAC,CAAC,MAAM,GACpB,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,MAAM;IACb,SAAS,sQAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,sQAAC,CAAC,MAAM,CAAC;IAC1C,QAAQ,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,OAAO,sQAAC,CAAC,KAAK,CAAC,sQAAC,CAAC,MAAM,CAAC;QACtB,WAAW,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,eAAe,sQAAC,CAAC,MAAM,GACpB,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,MAAM;QACb,SAAS,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,IAAI,GAAG,CAAC,GAAG;AACb;AAcO,MAAM,oBAAoB,CAC/B,WACA,QACA,eACA,UACA;IAEA,MAAM,SAA4B,EAAE;IAEpC,mBAAmB;IACnB,IAAI,CAAC,aAAa,UAAU,IAAI,OAAO,IAAI;QACzC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAa,SAAS;QAAyB;IACtE;IAEA,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;QACnC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAU,SAAS;QAAsB;IAChE;IAEA,mBAAmB;IACnB,IAAI,gBAAgB,GAAG;QACrB,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS;YACT;QACF;IACF;IAEA,IAAI,gBAAgB,UAAU;QAC5B,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC;YAC1D;QACF;IACF;IAEA,qDAAqD;IACrD,MAAM,gBAAgB,CAAC,cAAc,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,MAAM;IAC3E,IAAI,gBAAgB,GAAG;QACrB,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS;YACT;QACF;IACF;IAEA,6EAA6E;IAC7E,IAAI,WAAW,QAAQ,MAAM,GAAG,KAAK;QACnC,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS;YACT;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,MAAM,wBAAwB,CACnC,QACA,UACA;IAMA,MAAM,SAA4B,EAAE;IAEpC,kBAAkB;IAClB,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;QACnC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAU,SAAS;QAAsB;IAChE;IAEA,kCAAkC;IAClC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAS,SAAS;QAAsC;QAC7E,OAAO;YAAE,SAAS;YAAO;QAAO;IAClC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,IAAI;IACvB,UAAU,OAAO,CAAC,CAAC,MAAM;QACvB,kCAAkC;QAClC,IAAI,WAAW,GAAG,CAAC,KAAK,SAAS,GAAG;YAClC,OAAO,IAAI,CAAC;gBACV,OAAO;gBACP,SAAS;gBACT,WAAW,KAAK,SAAS;YAC3B;QACF,OAAO;YACL,WAAW,GAAG,CAAC,KAAK,SAAS;QAC/B;QAEA,iCAAiC;QACjC,MAAM,aAAa,kBACjB,KAAK,SAAS,EACd,QACA,KAAK,aAAa,EAClB,UACA,KAAK,OAAO;QAGd,4CAA4C;QAC5C,OAAO,IAAI,IAAI,WAAW,MAAM;IAClC;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,2BAA2B,CACtC,eACA;IAEA,IAAI,YAAY,GAAG;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,IAAI,gBAAgB,GAAG;QACrB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,IAAI,gBAAgB,UAAU;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAA6C;IAC/E;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,gBAAgB,WAAY,MAAM,OAAO;IACxE,OAAO;QAAE,SAAS;QAAM;IAAW;AACrC;AAGO,MAAM,yBAAyB,CAAC;IACrC,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,OAAO,MAAM,CAAC,EAAE,CAAC,OAAO;IAC1B;IAEA,OAAO,CAAC,wBAAwB,EAAE,OAAO,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,OAAO;AAClF;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK;QACzB,MAAM,MAAM,MAAM,SAAS,IAAI;QAC/B,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,GAAG,CAAC,IAAI,GAAG,EAAE;QACf;QACA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACd,OAAO;IACT,GAAG,CAAC;AACN;AAGO,MAAM,sBAAsB;IACjC,YAAY;IACZ,SAAS;IACT,cAAc;AAChB;AAEO,MAAM,kBAAkB,CAAC,OAAe,SAAiB;IAC9D,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;QACxB,OAAO;YACL,OAAO;YACP,SAAS,CAAC,QAAQ,EAAE,UAAU,OAAO,CAAC;QACxC;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/teacher/marks/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/db'\nimport {\n  MarkEntrySchema,\n  BulkMarkEntrySchema,\n  validateBulkMarkEntry,\n  formatValidationErrors\n} from '@/lib/marks-validation'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'TEACHER') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get teacher record for the logged-in user\n    const teacher = await prisma.teacher.findUnique({\n      where: {\n        userId: session.user.id\n      }\n    })\n\n    if (!teacher) {\n      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const examId = searchParams.get('examId')\n    const subjectId = searchParams.get('subjectId')\n    const classId = searchParams.get('classId')\n\n    const where: any = {\n      gradedByTeacherId: teacher.id\n    }\n    \n    if (examId && examId !== 'all') {\n      where.examId = examId\n    }\n    if (subjectId && subjectId !== 'all') {\n      where.exam = {\n        subjectId: subjectId\n      }\n    }\n    if (classId && classId !== 'all') {\n      where.student = {\n        currentClassId: classId\n      }\n    }\n\n    const marks = await prisma.mark.findMany({\n      where,\n      include: {\n        student: {\n          include: {\n            user: true,\n            currentClass: true,\n            currentSection: true\n          }\n        },\n        exam: {\n          include: {\n            subject: true,\n            term: true\n          }\n        }\n      },\n      orderBy: { createdAt: 'desc' }\n    })\n\n    return NextResponse.json(marks)\n  } catch (error) {\n    console.error('Error fetching teacher marks:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'TEACHER') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get teacher record for the logged-in user\n    const teacher = await prisma.teacher.findUnique({\n      where: {\n        userId: session.user.id\n      }\n    })\n\n    if (!teacher) {\n      return NextResponse.json({ error: 'Teacher not found' }, { status: 404 })\n    }\n\n    const body = await request.json()\n    \n    // Check if this is a bulk operation\n    if (body.marks && Array.isArray(body.marks)) {\n      // Bulk marks entry\n      const validation = BulkMarkEntrySchema.safeParse(body)\n      if (!validation.success) {\n        return NextResponse.json({\n          error: 'Validation failed',\n          details: validation.error.errors\n        }, { status: 400 })\n      }\n\n      const { examId, marks: marksData } = validation.data\n\n      // Validate that the exam exists and teacher can grade it\n      const exam = await prisma.exam.findUnique({\n        where: { id: examId },\n        include: { subject: true }\n      })\n\n      if (!exam) {\n        return NextResponse.json({ error: 'Exam not found' }, { status: 404 })\n      }\n\n      // Enhanced validation using our validation utility\n      const bulkValidation = validateBulkMarkEntry(examId, exam.maxMarks, marksData)\n      if (!bulkValidation.isValid) {\n        return NextResponse.json({\n          error: 'Validation failed',\n          message: formatValidationErrors(bulkValidation.errors),\n          details: bulkValidation.errors\n        }, { status: 400 })\n      }\n\n      // Process bulk marks entry\n      const results = []\n      for (const markData of marksData) {\n        try {\n          // Check if marks already exist\n          const existingMark = await prisma.mark.findUnique({\n            where: {\n              studentId_examId: {\n                studentId: markData.studentId,\n                examId\n              }\n            }\n          })\n\n          let mark\n          if (existingMark) {\n            // Update existing mark\n            mark = await prisma.mark.update({\n              where: { id: existingMark.id },\n              data: {\n                obtainedMarks: markData.obtainedMarks,\n                remarks: markData.remarks,\n                gradedByTeacherId: teacher.id\n              }\n            })\n          } else {\n            // Create new mark\n            mark = await prisma.mark.create({\n              data: {\n                studentId: markData.studentId,\n                examId,\n                obtainedMarks: markData.obtainedMarks,\n                remarks: markData.remarks,\n                gradedByTeacherId: teacher.id\n              }\n            })\n          }\n          results.push({ success: true, mark })\n        } catch (error) {\n          results.push({ \n            success: false, \n            error: error instanceof Error ? error.message : 'Unknown error',\n            studentId: markData.studentId\n          })\n        }\n      }\n\n      return NextResponse.json({ results })\n    } else {\n      // Single mark entry\n      const validation = MarkEntrySchema.safeParse(body)\n      if (!validation.success) {\n        return NextResponse.json({\n          error: 'Validation failed',\n          details: validation.error.errors\n        }, { status: 400 })\n      }\n\n      const { studentId, examId, obtainedMarks, remarks } = validation.data\n\n      // Validate that the exam exists and teacher can grade it\n      const exam = await prisma.exam.findUnique({\n        where: { id: examId },\n        include: { subject: true }\n      })\n\n      if (!exam) {\n        return NextResponse.json({ error: 'Exam not found' }, { status: 404 })\n      }\n\n      // Enhanced validation using our validation utility\n      const { validateMarkEntry } = await import('@/lib/marks-validation')\n      const markValidation = validateMarkEntry(studentId, examId, obtainedMarks, exam.maxMarks, remarks)\n      if (!markValidation.isValid) {\n        return NextResponse.json({\n          error: 'Validation failed',\n          message: formatValidationErrors(markValidation.errors),\n          details: markValidation.errors\n        }, { status: 400 })\n      }\n\n      // Check if marks already exist for this student-exam combination\n      const existingMark = await prisma.mark.findUnique({\n        where: {\n          studentId_examId: {\n            studentId,\n            examId\n          }\n        }\n      })\n\n      let mark\n      if (existingMark) {\n        // Update existing mark\n        mark = await prisma.mark.update({\n          where: { id: existingMark.id },\n          data: {\n            obtainedMarks,\n            remarks,\n            gradedByTeacherId: teacher.id\n          }\n        })\n      } else {\n        // Create new mark\n        mark = await prisma.mark.create({\n          data: {\n            studentId,\n            examId,\n            obtainedMarks,\n            remarks,\n            gradedByTeacherId: teacher.id\n          }\n        })\n      }\n\n      return NextResponse.json(mark)\n    }\n  } catch (error) {\n    console.error('Error creating/updating mark:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAOO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;YAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,4CAA4C;QAC5C,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,QAAa;YACjB,mBAAmB,QAAQ,EAAE;QAC/B;QAEA,IAAI,UAAU,WAAW,OAAO;YAC9B,MAAM,MAAM,GAAG;QACjB;QACA,IAAI,aAAa,cAAc,OAAO;YACpC,MAAM,IAAI,GAAG;gBACX,WAAW;YACb;QACF;QACA,IAAI,WAAW,YAAY,OAAO;YAChC,MAAM,OAAO,GAAG;gBACd,gBAAgB;YAClB;QACF;QAEA,MAAM,QAAQ,MAAM,8JAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC;YACA,SAAS;gBACP,SAAS;oBACP,SAAS;wBACP,MAAM;wBACN,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBACA,MAAM;oBACJ,SAAS;wBACP,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;YAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,4CAA4C;QAC5C,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,oCAAoC;QACpC,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;YAC3C,mBAAmB;YACnB,MAAM,aAAa,4LAAmB,CAAC,SAAS,CAAC;YACjD,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;oBACP,SAAS,WAAW,KAAK,CAAC,MAAM;gBAClC,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,MAAM,EAAE,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,WAAW,IAAI;YAEpD,yDAAyD;YACzD,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,OAAO;oBAAE,IAAI;gBAAO;gBACpB,SAAS;oBAAE,SAAS;gBAAK;YAC3B;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,iSAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAiB,GAAG;oBAAE,QAAQ;gBAAI;YACtE;YAEA,mDAAmD;YACnD,MAAM,iBAAiB,IAAA,8LAAqB,EAAC,QAAQ,KAAK,QAAQ,EAAE;YACpE,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;oBACP,SAAS,IAAA,+LAAsB,EAAC,eAAe,MAAM;oBACrD,SAAS,eAAe,MAAM;gBAChC,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,2BAA2B;YAC3B,MAAM,UAAU,EAAE;YAClB,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI;oBACF,+BAA+B;oBAC/B,MAAM,eAAe,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBAChD,OAAO;4BACL,kBAAkB;gCAChB,WAAW,SAAS,SAAS;gCAC7B;4BACF;wBACF;oBACF;oBAEA,IAAI;oBACJ,IAAI,cAAc;wBAChB,uBAAuB;wBACvB,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;4BAC9B,OAAO;gCAAE,IAAI,aAAa,EAAE;4BAAC;4BAC7B,MAAM;gCACJ,eAAe,SAAS,aAAa;gCACrC,SAAS,SAAS,OAAO;gCACzB,mBAAmB,QAAQ,EAAE;4BAC/B;wBACF;oBACF,OAAO;wBACL,kBAAkB;wBAClB,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;4BAC9B,MAAM;gCACJ,WAAW,SAAS,SAAS;gCAC7B;gCACA,eAAe,SAAS,aAAa;gCACrC,SAAS,SAAS,OAAO;gCACzB,mBAAmB,QAAQ,EAAE;4BAC/B;wBACF;oBACF;oBACA,QAAQ,IAAI,CAAC;wBAAE,SAAS;wBAAM;oBAAK;gBACrC,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC;wBACX,SAAS;wBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAChD,WAAW,SAAS,SAAS;oBAC/B;gBACF;YACF;YAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE;YAAQ;QACrC,OAAO;YACL,oBAAoB;YACpB,MAAM,aAAa,wLAAe,CAAC,SAAS,CAAC;YAC7C,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;oBACP,SAAS,WAAW,KAAK,CAAC,MAAM;gBAClC,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,WAAW,IAAI;YAErE,yDAAyD;YACzD,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,OAAO;oBAAE,IAAI;gBAAO;gBACpB,SAAS;oBAAE,SAAS;gBAAK;YAC3B;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,iSAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAiB,GAAG;oBAAE,QAAQ;gBAAI;YACtE;YAEA,mDAAmD;YACnD,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,iBAAiB,kBAAkB,WAAW,QAAQ,eAAe,KAAK,QAAQ,EAAE;YAC1F,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;oBACP,SAAS,IAAA,+LAAsB,EAAC,eAAe,MAAM;oBACrD,SAAS,eAAe,MAAM;gBAChC,GAAG;oBAAE,QAAQ;gBAAI;YACnB;YAEA,iEAAiE;YACjE,MAAM,eAAe,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChD,OAAO;oBACL,kBAAkB;wBAChB;wBACA;oBACF;gBACF;YACF;YAEA,IAAI;YACJ,IAAI,cAAc;gBAChB,uBAAuB;gBACvB,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC9B,OAAO;wBAAE,IAAI,aAAa,EAAE;oBAAC;oBAC7B,MAAM;wBACJ;wBACA;wBACA,mBAAmB,QAAQ,EAAE;oBAC/B;gBACF;YACF,OAAO;gBACL,kBAAkB;gBAClB,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC9B,MAAM;wBACJ;wBACA;wBACA;wBACA;wBACA,mBAAmB,QAAQ,EAAE;oBAC/B;gBACF;YACF;YAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}