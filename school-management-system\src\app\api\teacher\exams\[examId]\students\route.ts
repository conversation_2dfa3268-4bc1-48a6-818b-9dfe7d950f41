import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { examId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { examId } = await params

    // Get the exam details first
    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      include: {
        subject: {
          include: {
            class: true
          }
        },
        term: true
      }
    })

    if (!exam) {
      return NextResponse.json({ error: 'Exam not found' }, { status: 404 })
    }

    // Get all students in the class for this exam's subject
    const students = await prisma.student.findMany({
      where: {
        currentClassId: exam.subject.classId
      },
      include: {
        user: true,
        currentClass: true,
        currentSection: true,
        marks: {
          where: {
            examId: examId
          }
        }
      },
      orderBy: [
        { currentSection: { name: 'asc' } },
        { rollNumber: 'asc' },
        { user: { firstName: 'asc' } }
      ]
    })

    // Transform the data to include mark information
    const studentsWithMarks = students.map(student => ({
      id: student.id,
      admissionNo: student.admissionNo,
      rollNumber: student.rollNumber,
      firstName: student.user.firstName,
      lastName: student.user.lastName,
      email: student.user.email,
      className: student.currentClass?.name,
      sectionName: student.currentSection?.name,
      currentMark: student.marks[0] || null, // There should be at most one mark per student per exam
      hasMarks: student.marks.length > 0
    }))

    return NextResponse.json({
      exam,
      students: studentsWithMarks
    })
  } catch (error) {
    console.error('Error fetching exam students:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
