// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/(auth)/login/page.tsx
{
  const handler = {} as typeof import("../../src/app/(auth)/login/page.js")
  handler satisfies AppPageConfig<"/login">
}

// Validate ../../src/app/(dash)/admin/attendance/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/attendance/page.js")
  handler satisfies AppPageConfig<"/admin/attendance">
}

// Validate ../../src/app/(dash)/admin/classes/new/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/classes/new/page.js")
  handler satisfies AppPageConfig<"/admin/classes/new">
}

// Validate ../../src/app/(dash)/admin/classes/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/classes/page.js")
  handler satisfies AppPageConfig<"/admin/classes">
}

// Validate ../../src/app/(dash)/admin/exams/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/exams/page.js")
  handler satisfies AppPageConfig<"/admin/exams">
}

// Validate ../../src/app/(dash)/admin/marks/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/marks/page.js")
  handler satisfies AppPageConfig<"/admin/marks">
}

// Validate ../../src/app/(dash)/admin/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/page.js")
  handler satisfies AppPageConfig<"/admin">
}

// Validate ../../src/app/(dash)/admin/reports/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/reports/page.js")
  handler satisfies AppPageConfig<"/admin/reports">
}

// Validate ../../src/app/(dash)/admin/settings/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/settings/page.js")
  handler satisfies AppPageConfig<"/admin/settings">
}

// Validate ../../src/app/(dash)/admin/students/[id]/edit/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/students/[id]/edit/page.js")
  handler satisfies AppPageConfig<"/admin/students/[id]/edit">
}

// Validate ../../src/app/(dash)/admin/students/[id]/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/students/[id]/page.js")
  handler satisfies AppPageConfig<"/admin/students/[id]">
}

// Validate ../../src/app/(dash)/admin/students/bulk/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/students/bulk/page.js")
  handler satisfies AppPageConfig<"/admin/students/bulk">
}

// Validate ../../src/app/(dash)/admin/students/new/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/students/new/page.js")
  handler satisfies AppPageConfig<"/admin/students/new">
}

// Validate ../../src/app/(dash)/admin/students/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/students/page.js")
  handler satisfies AppPageConfig<"/admin/students">
}

// Validate ../../src/app/(dash)/admin/subjects/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/subjects/page.js")
  handler satisfies AppPageConfig<"/admin/subjects">
}

// Validate ../../src/app/(dash)/admin/teachers/[id]/edit/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/teachers/[id]/edit/page.js")
  handler satisfies AppPageConfig<"/admin/teachers/[id]/edit">
}

// Validate ../../src/app/(dash)/admin/teachers/[id]/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/teachers/[id]/page.js")
  handler satisfies AppPageConfig<"/admin/teachers/[id]">
}

// Validate ../../src/app/(dash)/admin/teachers/new/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/teachers/new/page.js")
  handler satisfies AppPageConfig<"/admin/teachers/new">
}

// Validate ../../src/app/(dash)/admin/teachers/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/admin/teachers/page.js")
  handler satisfies AppPageConfig<"/admin/teachers">
}

// Validate ../../src/app/(dash)/student/attendance/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/student/attendance/page.js")
  handler satisfies AppPageConfig<"/student/attendance">
}

// Validate ../../src/app/(dash)/student/marks/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/student/marks/page.js")
  handler satisfies AppPageConfig<"/student/marks">
}

// Validate ../../src/app/(dash)/student/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/student/page.js")
  handler satisfies AppPageConfig<"/student">
}

// Validate ../../src/app/(dash)/student/reports/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/student/reports/page.js")
  handler satisfies AppPageConfig<"/student/reports">
}

// Validate ../../src/app/(dash)/teacher/attendance/mark/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/teacher/attendance/mark/page.js")
  handler satisfies AppPageConfig<"/teacher/attendance/mark">
}

// Validate ../../src/app/(dash)/teacher/attendance/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/teacher/attendance/page.js")
  handler satisfies AppPageConfig<"/teacher/attendance">
}

// Validate ../../src/app/(dash)/teacher/marks/[examId]/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/teacher/marks/[examId]/page.js")
  handler satisfies AppPageConfig<"/teacher/marks/[examId]">
}

// Validate ../../src/app/(dash)/teacher/marks/[examId]/view/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/teacher/marks/[examId]/view/page.js")
  handler satisfies AppPageConfig<"/teacher/marks/[examId]/view">
}

// Validate ../../src/app/(dash)/teacher/marks/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/teacher/marks/page.js")
  handler satisfies AppPageConfig<"/teacher/marks">
}

// Validate ../../src/app/(dash)/teacher/page.tsx
{
  const handler = {} as typeof import("../../src/app/(dash)/teacher/page.js")
  handler satisfies AppPageConfig<"/teacher">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/test-login/page.tsx
{
  const handler = {} as typeof import("../../src/app/test-login/page.js")
  handler satisfies AppPageConfig<"/test-login">
}

// Validate ../../src/app/unauthorized/page.tsx
{
  const handler = {} as typeof import("../../src/app/unauthorized/page.js")
  handler satisfies AppPageConfig<"/unauthorized">
}

// Validate ../../src/app/api/admin/attendance/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/attendance/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/attendance">
}

// Validate ../../src/app/api/admin/classes/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/classes/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/classes/[id]">
}

// Validate ../../src/app/api/admin/classes/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/classes/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/classes">
}

// Validate ../../src/app/api/admin/dashboard/stats/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/dashboard/stats/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/dashboard/stats">
}

// Validate ../../src/app/api/admin/exams/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/exams/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/exams">
}

// Validate ../../src/app/api/admin/marks/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/marks/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/marks">
}

// Validate ../../src/app/api/admin/reports/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/reports/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/reports">
}

// Validate ../../src/app/api/admin/sections/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/sections/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/sections">
}

// Validate ../../src/app/api/admin/settings/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/settings/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/settings">
}

// Validate ../../src/app/api/admin/students/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/students/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/students/[id]">
}

// Validate ../../src/app/api/admin/students/bulk/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/students/bulk/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/students/bulk">
}

// Validate ../../src/app/api/admin/students/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/students/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/students">
}

// Validate ../../src/app/api/admin/subjects/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/subjects/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/subjects">
}

// Validate ../../src/app/api/admin/teachers/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/teachers/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/teachers/[id]">
}

// Validate ../../src/app/api/admin/teachers/route.ts
{
  const handler = {} as typeof import("../../src/app/api/admin/teachers/route.js")
  handler satisfies RouteHandlerConfig<"/api/admin/teachers">
}

// Validate ../../src/app/api/auth/[...nextauth]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/auth/[...nextauth]/route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/[...nextauth]">
}

// Validate ../../src/app/api/student/attendance/route.ts
{
  const handler = {} as typeof import("../../src/app/api/student/attendance/route.js")
  handler satisfies RouteHandlerConfig<"/api/student/attendance">
}

// Validate ../../src/app/api/student/dashboard/stats/route.ts
{
  const handler = {} as typeof import("../../src/app/api/student/dashboard/stats/route.js")
  handler satisfies RouteHandlerConfig<"/api/student/dashboard/stats">
}

// Validate ../../src/app/api/student/marks/route.ts
{
  const handler = {} as typeof import("../../src/app/api/student/marks/route.js")
  handler satisfies RouteHandlerConfig<"/api/student/marks">
}

// Validate ../../src/app/api/student/reports/route.ts
{
  const handler = {} as typeof import("../../src/app/api/student/reports/route.js")
  handler satisfies RouteHandlerConfig<"/api/student/reports">
}

// Validate ../../src/app/api/teacher/attendance/route.ts
{
  const handler = {} as typeof import("../../src/app/api/teacher/attendance/route.js")
  handler satisfies RouteHandlerConfig<"/api/teacher/attendance">
}

// Validate ../../src/app/api/teacher/dashboard/stats/route.ts
{
  const handler = {} as typeof import("../../src/app/api/teacher/dashboard/stats/route.js")
  handler satisfies RouteHandlerConfig<"/api/teacher/dashboard/stats">
}

// Validate ../../src/app/api/teacher/exams/[examId]/students/route.ts
{
  const handler = {} as typeof import("../../src/app/api/teacher/exams/[examId]/students/route.js")
  handler satisfies RouteHandlerConfig<"/api/teacher/exams/[examId]/students">
}

// Validate ../../src/app/api/teacher/exams/route.ts
{
  const handler = {} as typeof import("../../src/app/api/teacher/exams/route.js")
  handler satisfies RouteHandlerConfig<"/api/teacher/exams">
}

// Validate ../../src/app/api/teacher/marks/route.ts
{
  const handler = {} as typeof import("../../src/app/api/teacher/marks/route.js")
  handler satisfies RouteHandlerConfig<"/api/teacher/marks">
}





// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
