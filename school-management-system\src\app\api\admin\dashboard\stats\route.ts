import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all the statistics in parallel for better performance
    const [
      totalStudents,
      totalTeachers,
      totalClasses,
      totalSubjects,
      attendanceStats,
      marksStats,
      activeStudents,
      activeTeachers
    ] = await Promise.all([
      // Total students count
      prisma.student.count(),
      
      // Total teachers count
      prisma.teacher.count(),
      
      // Total classes count
      prisma.class.count(),
      
      // Total subjects count
      prisma.subject.count(),
      
      // Attendance statistics (last 30 days)
      prisma.attendance.aggregate({
        where: {
          date: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        _count: {
          id: true
        }
      }).then(async (total) => {
        const present = await prisma.attendance.count({
          where: {
            date: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            status: 'PRESENT'
          }
        })
        return {
          total: total._count.id,
          present,
          rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0
        }
      }),
      
      // Marks statistics (average marks)
      prisma.mark.aggregate({
        _avg: {
          obtainedMarks: true
        },
        _count: {
          id: true
        }
      }),
      
      // Active students (students with recent activity)
      prisma.student.count({
        where: {
          user: {
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        }
      }),
      
      // Active teachers (teachers with recent activity)
      prisma.teacher.count({
        where: {
          user: {
            lastLoginAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        }
      })
    ])

    // Calculate additional statistics
    const stats = {
      totalStudents,
      totalTeachers,
      totalClasses,
      totalSubjects,
      activeStudents,
      activeTeachers,
      attendanceRate: Math.round(attendanceStats.rate * 10) / 10, // Round to 1 decimal
      averageMarks: marksStats._avg.obtainedMarks 
        ? Math.round(marksStats._avg.obtainedMarks * 10) / 10 
        : 0,
      totalMarksRecords: marksStats._count.id,
      totalAttendanceRecords: attendanceStats.total
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching admin dashboard stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
