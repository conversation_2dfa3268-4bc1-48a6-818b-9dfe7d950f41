{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/students/student-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StudentForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call StudentForm() from the server but StudentForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/students/student-form.tsx <module evaluation>\",\n    \"StudentForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,yZAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,mGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/students/student-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StudentForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call StudentForm() from the server but StudentForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/students/student-form.tsx\",\n    \"StudentForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,yZAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuU,GACpW,qGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/layout/dashboard-layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/students/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["import { getServerSession } from 'next-auth';\nimport { redirect, notFound } from 'next/navigation';\nimport { authOptions } from '@/lib/auth';\nimport { prisma as db } from '@/lib/db';\nimport { hasPermission } from '@/lib/rbac';\nimport { StudentForm } from '@/components/students/student-form';\nimport DashboardLayout from '@/components/layout/dashboard-layout';\nimport { adminNavigation } from '@/lib/navigation';\nimport { Users, Edit } from 'lucide-react';\n\ninterface EditStudentPageProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nexport default async function EditStudentPage({ params }: EditStudentPageProps) {\n  // Temporarily bypass authentication for testing\n  // const session = await getServerSession(authOptions);\n\n  // if (!session?.user) {\n  //   redirect('/login');\n  // }\n\n  // if (!hasPermission(session.user.role as any, 'students:write')) {\n  //   redirect('/unauthorized');\n  // }\n\n  const { id } = await params;\n\n  // Fetch student with user data and classes\n  const [student, classes] = await Promise.all([\n    db.student.findUnique({\n      where: { id },\n      include: {\n        user: true,\n        currentClass: {\n          include: {\n            sections: true,\n          },\n        },\n      },\n    }),\n    db.class.findMany({\n      include: {\n        sections: true,\n      },\n      orderBy: [\n        { name: 'asc' },\n      ],\n    }),\n  ]);\n\n  if (!student) {\n    notFound();\n  }\n\n  // Transform student data for the form\n  const studentData = {\n    id: student.id,\n    firstName: student.user.firstName,\n    lastName: student.user.lastName,\n    email: student.user.email,\n    dateOfBirth: student.dob.toISOString().split('T')[0],\n    gender: student.gender,\n    phoneNumber: student.user.phone || '',\n    address: student.address || '',\n    emergencyContact: student.guardianName || '',\n    emergencyPhone: student.guardianPhone || '',\n    admissionDate: student.createdAt.toISOString().split('T')[0],\n    classId: student.currentClassId || '',\n    parentName: student.guardianName || '',\n    parentPhone: student.guardianPhone || '',\n    parentEmail: '', // Not in schema, using empty string\n  };\n\n  return (\n    <DashboardLayout \n      title=\"Edit Student\"\n      navigation={adminNavigation}\n    >\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">\n            Edit Student: {student.user.firstName} {student.user.lastName}\n          </h1>\n          <p className=\"text-muted-foreground\">\n            Update student information and details\n          </p>\n        </div>\n        \n        <StudentForm \n          student={studentData}\n          classes={classes} \n          mode=\"edit\" \n        />\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAEA;AAEA;AACA;AACA;;;;;;;AASe,eAAe,gBAAgB,EAAE,MAAM,EAAwB;IAC5E,gDAAgD;IAChD,uDAAuD;IAEvD,wBAAwB;IACxB,wBAAwB;IACxB,IAAI;IAEJ,oEAAoE;IACpE,+BAA+B;IAC/B,IAAI;IAEJ,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,2CAA2C;IAC3C,MAAM,CAAC,SAAS,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC3C,4JAAE,CAAC,OAAO,CAAC,UAAU,CAAC;YACpB,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,MAAM;gBACN,cAAc;oBACZ,SAAS;wBACP,UAAU;oBACZ;gBACF;YACF;QACF;QACA,4JAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;YAChB,SAAS;gBACP,UAAU;YACZ;YACA,SAAS;gBACP;oBAAE,MAAM;gBAAM;aACf;QACH;KACD;IAED,IAAI,CAAC,SAAS;QACZ,IAAA,kVAAQ;IACV;IAEA,sCAAsC;IACtC,MAAM,cAAc;QAClB,IAAI,QAAQ,EAAE;QACd,WAAW,QAAQ,IAAI,CAAC,SAAS;QACjC,UAAU,QAAQ,IAAI,CAAC,QAAQ;QAC/B,OAAO,QAAQ,IAAI,CAAC,KAAK;QACzB,aAAa,QAAQ,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,QAAQ,QAAQ,MAAM;QACtB,aAAa,QAAQ,IAAI,CAAC,KAAK,IAAI;QACnC,SAAS,QAAQ,OAAO,IAAI;QAC5B,kBAAkB,QAAQ,YAAY,IAAI;QAC1C,gBAAgB,QAAQ,aAAa,IAAI;QACzC,eAAe,QAAQ,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5D,SAAS,QAAQ,cAAc,IAAI;QACnC,YAAY,QAAQ,YAAY,IAAI;QACpC,aAAa,QAAQ,aAAa,IAAI;QACtC,aAAa;IACf;IAEA,qBACE,+XAAC,gMAAe;QACd,OAAM;QACN,YAAY,6KAAe;kBAE3B,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;;sCACC,+XAAC;4BAAG,WAAU;;gCAAoC;gCACjC,QAAQ,IAAI,CAAC,SAAS;gCAAC;gCAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;sCAE/D,+XAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,+XAAC,kMAAW;oBACV,SAAS;oBACT,SAAS;oBACT,MAAK;;;;;;;;;;;;;;;;;AAKf", "debugId": null}}]}