{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,6TAA0B;AAE/C,MAAM,sBAAsB,gUAA6B;AAEzD,MAAM,oBAAoB,8TAA2B;AAErD,MAAM,qBAAqB,+TAA4B;AAEvD,MAAM,kBAAkB,4TAAyB;AAEjD,MAAM,yBAAyB,mUAAgC;AAE/D,MAAM,uCAAyB,oWAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,+XAAC,4VAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,oWAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,oWAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,+XAAC,+TAA4B;kBAC3B,cAAA,+XAAC,gUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,2JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,oWAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,6TAA0B;QACzB,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6TAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,oWAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,+XAAC,qUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,mUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,qUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,sUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,oWAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,8TAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,8TAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,kMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,+XAAC,8LAAY;;0BACX,+XAAC,qMAAmB;gBAAC,OAAO;0BAC1B,cAAA,+XAAC,8KAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,gBAAgB,UAAU,SAAS,QAAQ,KAAK,CAAC;;sCAErE,+XAAC,6TAAG;4BAAC,WAAU;;;;;;sCACf,+XAAC,gUAAI;4BAAC,WAAU;;;;;;sCAChB,+XAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,+XAAC,qMAAmB;gBAAC,OAAM;;kCACzB,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,6TAAG;gCAAC,WAAU;;;;;;0CACf,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,gUAAI;gCAAC,WAAU;;;;;;0CAChB,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,yUAAO;gCAAC,WAAU;;;;;;0CACnB,+XAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,gUAAI;IACJ,QAAA,sUAAM;IACN,UAAA,4UAAQ;IACR,OAAA,mUAAK;IACL,UAAA,gVAAQ;IACR,eAAA,+VAAa;IACb,UAAA,gVAAQ;IACR,WAAA,qVAAS;IACT,UAAA,4UAAQ;IACR,MAAA,iUAAI;IACJ,UAAA,4UAAQ;IACR,MAAA,gUAAI;IACJ,MAAA,gUAAI;IACJ,MAAA,yUAAI;IACJ,eAAA,+VAAa;IACb,OAAA,mUAAK;AACP;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB;IAC3F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IACpC,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,uSAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,iUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,+XAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,+XAAC,uTAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,+XAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,+XAAC,8KAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,+XAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;;;;;gCAYhC;;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,sUAAM;oCAAC,WAAU;;;;;;8CAClB,+XAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,+XAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,+XAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,+XAAC,8KAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,+XAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;;;;;4BASjC;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,8KAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,+XAAC,gUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,sUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,+XAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,4LAAW;;;;;kDAEZ,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,+XAAC,gUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAU;gEAAE,SAAS,MAAM;;;;;;;sEAE7C,+XAAC;4DAAE,WAAU;sEACV,SAAS,MAAM,MAAM;;;;;;;;;;;;8DAG1B,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,8KAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,+XAAC,0UAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/student/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useSession } from 'next-auth/react'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport DashboardLayout from '@/components/layout/dashboard-layout'\r\nimport { studentNavigation } from '@/lib/navigation'\r\nimport {\r\n  TrendingUp,\r\n  Bell,\r\n  GraduationCap,\r\n  Target,\r\n  Award,\r\n  Calendar,\r\n  FileText,\r\n  Clock\r\n} from 'lucide-react'\r\n\r\nexport default function StudentDashboard() {\r\n  const { data: session } = useSession()\r\n\r\n  // Mock data - in real app, this would come from API\r\n  const stats = {\r\n    currentClass: 'Grade 8A',\r\n    rollNumber: '8A01',\r\n    attendanceRate: 96.5,\r\n    averageMarks: 85.2,\r\n    totalSubjects: 5,\r\n    upcomingExams: 1\r\n  }\r\n\r\n  const subjects = [\r\n    {\r\n      name: 'Mathematics',\r\n      teacher: 'Mr. <PERSON>',\r\n      marks: 92,\r\n      grade: 'A+',\r\n      attendance: 98\r\n    },\r\n    {\r\n      name: 'English',\r\n      teacher: 'Ms. <PERSON>',\r\n      marks: 88,\r\n      grade: 'A',\r\n      attendance: 95\r\n    },\r\n    {\r\n      name: 'Science',\r\n      teacher: 'Dr. <PERSON>',\r\n      marks: 85,\r\n      grade: 'A',\r\n      attendance: 97\r\n    },\r\n    {\r\n      name: 'Social Studies',\r\n      teacher: 'Mrs. <PERSON>',\r\n      marks: 82,\r\n      grade: 'B+',\r\n      attendance: 94\r\n    },\r\n    {\r\n      name: 'Computer Science',\r\n      teacher: 'Mr. <PERSON>',\r\n      marks: 90,\r\n      grade: 'A+',\r\n      attendance: 96\r\n    }\r\n  ]\r\n\r\n  const quickActions = [\r\n    {\r\n      title: 'View Marks',\r\n      description: 'Check your latest grades',\r\n      icon: Award,\r\n      href: '/student/marks',\r\n      color: 'bg-blue-500'\r\n    },\r\n    {\r\n      title: 'Attendance History',\r\n      description: 'View your attendance record',\r\n      icon: Calendar,\r\n      href: '/student/attendance',\r\n      color: 'bg-green-500'\r\n    },\r\n    {\r\n      title: 'Download Report',\r\n      description: 'Get your report card',\r\n      icon: FileText,\r\n      href: '/student/reports',\r\n      color: 'bg-purple-500'\r\n    },\r\n    {\r\n      title: 'View Schedule',\r\n      description: 'Check your timetable',\r\n      icon: Clock,\r\n      href: '/student/schedule',\r\n      color: 'bg-orange-500'\r\n    }\r\n  ]\r\n\r\n  return (\r\n    <DashboardLayout title=\"Student Dashboard\" navigation={studentNavigation}>\r\n      <div className=\"space-y-6\">\r\n        {/* Welcome Section */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\r\n            Welcome back, {session?.user?.firstName || 'Student'}!\r\n          </h2>\r\n          <p className=\"text-gray-600\">\r\n            Here's your academic overview and progress summary.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Personal Info Card */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Personal Information</h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-gray-600\">Class:</span>\r\n                <span className=\"font-medium\">{stats.currentClass}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-gray-600\">Roll Number:</span>\r\n                <span className=\"font-medium\">{stats.rollNumber}</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-gray-600\">Total Subjects:</span>\r\n                <span className=\"font-medium\">{stats.totalSubjects}</span>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-gray-600\">Attendance Rate:</span>\r\n                <span className=\"font-medium text-green-600\">{stats.attendanceRate}%</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-gray-600\">Average Marks:</span>\r\n                <span className=\"font-medium text-blue-600\">{stats.averageMarks}%</span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-gray-600\">Upcoming Exams:</span>\r\n                <span className=\"font-medium\">{stats.upcomingExams}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Attendance</CardTitle>\r\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold text-green-600\">{stats.attendanceRate}%</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                This month\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Average Marks</CardTitle>\r\n              <Award className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold text-blue-600\">{stats.averageMarks}%</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Current term\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">GPA</CardTitle>\r\n              <Target className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold text-purple-600\">3.8</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Out of 4.0\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Rank</CardTitle>\r\n              <GraduationCap className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold text-orange-600\">5th</div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                In class\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Subject Performance */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Subject Performance</h3>\r\n          <div className=\"space-y-3\">\r\n            {subjects.map((subject) => (\r\n              <div key={subject.name} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n                  <div>\r\n                    <p className=\"font-medium\">{subject.name}</p>\r\n                    <p className=\"text-sm text-gray-600\">{subject.teacher}</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"text-center\">\r\n                    <p className=\"text-sm text-gray-600\">Marks</p>\r\n                    <p className=\"font-medium text-blue-600\">{subject.marks}%</p>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <p className=\"text-sm text-gray-600\">Grade</p>\r\n                    <p className=\"font-medium text-green-600\">{subject.grade}</p>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <p className=\"text-sm text-gray-600\">Attendance</p>\r\n                    <p className=\"font-medium text-purple-600\">{subject.attendance}%</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quick Actions */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            {quickActions.map((action) => (\r\n              <Card key={action.title} className=\"cursor-pointer hover:shadow-md transition-shadow\">\r\n                <CardContent className=\"p-4\">\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <div className={`p-2 rounded-lg ${action.color}`}>\r\n                      <action.icon className=\"h-5 w-5 text-white\" />\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"font-medium text-sm\">{action.title}</h4>\r\n                      <p className=\"text-xs text-gray-500\">{action.description}</p>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Recent Activities */}\r\n        <div className=\"bg-white rounded-lg border p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activities</h3>\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center space-x-3 p-3 bg-green-50 rounded-lg\">\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n              <div className=\"flex-1\">\r\n                <p className=\"text-sm font-medium\">Marks uploaded</p>\r\n                <p className=\"text-xs text-gray-500\">Mathematics - Unit Test 1: 92%</p>\r\n              </div>\r\n              <span className=\"text-xs text-gray-500\">2 days ago</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-3 p-3 bg-blue-50 rounded-lg\">\r\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n              <div className=\"flex-1\">\r\n                <p className=\"text-sm font-medium\">Attendance marked</p>\r\n                <p className=\"text-xs text-gray-500\">Present in all classes today</p>\r\n              </div>\r\n              <span className=\"text-xs text-gray-500\">1 day ago</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\r\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\r\n              <div className=\"flex-1\">\r\n                <p className=\"text-sm font-medium\">Report card generated</p>\r\n                <p className=\"text-xs text-gray-500\">Term 1 report available for download</p>\r\n              </div>\r\n              <span className=\"text-xs text-gray-500\">1 week ago</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </DashboardLayout>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAiBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IAEpC,oDAAoD;IACpD,MAAM,QAAQ;QACZ,cAAc;QACd,YAAY;QACZ,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,WAAW;QACf;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,YAAY;QACd;QACA;YACE,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,YAAY;QACd;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,mUAAK;YACX,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,4UAAQ;YACd,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,gVAAQ;YACd,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,mUAAK;YACX,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,+XAAC,gMAAe;QAAC,OAAM;QAAoB,YAAY,+KAAiB;kBACtE,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;;gCAA2C;gCACxC,SAAS,MAAM,aAAa;gCAAU;;;;;;;sCAEvD,+XAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,+XAAC;oDAAK,WAAU;8DAAe,MAAM,YAAY;;;;;;;;;;;;sDAEnD,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,+XAAC;oDAAK,WAAU;8DAAe,MAAM,UAAU;;;;;;;;;;;;sDAEjD,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,+XAAC;oDAAK,WAAU;8DAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;8CAGtD,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,+XAAC;oDAAK,WAAU;;wDAA8B,MAAM,cAAc;wDAAC;;;;;;;;;;;;;sDAErE,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,+XAAC;oDAAK,WAAU;;wDAA6B,MAAM,YAAY;wDAAC;;;;;;;;;;;;;sDAElE,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,+XAAC;oDAAK,WAAU;8DAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO1D,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,sVAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,+XAAC,iLAAW;;sDACV,+XAAC;4CAAI,WAAU;;gDAAqC,MAAM,cAAc;gDAAC;;;;;;;sDACzE,+XAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,mUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,+XAAC,iLAAW;;sDACV,+XAAC;4CAAI,WAAU;;gDAAoC,MAAM,YAAY;gDAAC;;;;;;;sDACtE,+XAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,sUAAM;4CAAC,WAAU;;;;;;;;;;;;8CAEpB,+XAAC,iLAAW;;sDACV,+XAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,+XAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,+VAAa;4CAAC,WAAU;;;;;;;;;;;;8CAE3B,+XAAC,iLAAW;;sDACV,+XAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,+XAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,+XAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,+XAAC;oCAAuB,WAAU;;sDAChC,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;;;;;8DACf,+XAAC;;sEACC,+XAAC;4DAAE,WAAU;sEAAe,QAAQ,IAAI;;;;;;sEACxC,+XAAC;4DAAE,WAAU;sEAAyB,QAAQ,OAAO;;;;;;;;;;;;;;;;;;sDAGzD,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,+XAAC;4DAAE,WAAU;;gEAA6B,QAAQ,KAAK;gEAAC;;;;;;;;;;;;;8DAE1D,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,+XAAC;4DAAE,WAAU;sEAA8B,QAAQ,KAAK;;;;;;;;;;;;8DAE1D,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,+XAAC;4DAAE,WAAU;;gEAA+B,QAAQ,UAAU;gEAAC;;;;;;;;;;;;;;;;;;;;mCAnB3D,QAAQ,IAAI;;;;;;;;;;;;;;;;8BA4B5B,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,+XAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,+XAAC,0KAAI;oCAAoB,WAAU;8CACjC,cAAA,+XAAC,iLAAW;wCAAC,WAAU;kDACrB,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAW,CAAC,eAAe,EAAE,OAAO,KAAK,EAAE;8DAC9C,cAAA,+XAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEzB,+XAAC;;sEACC,+XAAC;4DAAG,WAAU;sEAAuB,OAAO,KAAK;;;;;;sEACjD,+XAAC;4DAAE,WAAU;sEAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;mCARrD,OAAO,KAAK;;;;;;;;;;;;;;;;8BAkB7B,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;;;;;;sDACf,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,+XAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,+XAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;;;;;;sDACf,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,+XAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,+XAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;;;;;;sDACf,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,+XAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,+XAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}]}